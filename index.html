<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>API Interface Dashboard</title>
    <link rel="stylesheet" href="css/style.css" />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
  </head>
  <body>
    <header class="header">
      <div class="container">
        <h1><i class="fas fa-code"></i> API Interface Dashboard</h1>
        <p>Interact with REST APIs easily</p>
      </div>
    </header>

    <main class="main-content">
      <div class="container">
        <!-- API Status Section -->
        <section class="api-status">
          <h2><i class="fas fa-signal"></i> API Status</h2>
          <div class="status-indicator" id="apiStatus">
            <span class="status-dot"></span>
            <span class="status-text">Checking...</span>
          </div>
        </section>

        <!-- Users Management Section -->
        <section class="users-section">
          <h2><i class="fas fa-users"></i> Users Management</h2>

          <!-- Add New User Form -->
          <div class="form-container">
            <h3>Add New User</h3>
            <form id="addUserForm" class="user-form">
              <div class="form-group">
                <label for="userName">Name:</label>
                <input type="text" id="userName" name="name" required />
              </div>
              <div class="form-group">
                <label for="userEmail">Email:</label>
                <input type="email" id="userEmail" name="email" required />
              </div>
              <div class="form-group">
                <label for="userPhone">Phone:</label>
                <input type="tel" id="userPhone" name="phone" required />
              </div>
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-plus"></i> Add User
              </button>
            </form>
          </div>

          <!-- Users List -->
          <div class="users-container">
            <div class="section-header">
              <h3>Users List</h3>
              <button id="refreshUsers" class="btn btn-secondary">
                <i class="fas fa-refresh"></i> Refresh
              </button>
            </div>
            <div id="loadingSpinner" class="loading">
              <i class="fas fa-spinner fa-spin"></i> Loading...
            </div>
            <div id="usersList" class="users-grid"></div>
          </div>
        </section>

        <!-- Posts Management Section -->
        <section class="posts-section">
          <h2><i class="fas fa-file-alt"></i> Posts Management</h2>

          <!-- Add New Post Form -->
          <div class="form-container">
            <h3>Create New Post</h3>
            <form id="addPostForm" class="post-form">
              <div class="form-group">
                <label for="postTitle">Title:</label>
                <input type="text" id="postTitle" name="title" required />
              </div>
              <div class="form-group">
                <label for="postBody">Content:</label>
                <textarea
                  id="postBody"
                  name="body"
                  rows="4"
                  required
                ></textarea>
              </div>
              <div class="form-group">
                <label for="postUserId">User ID:</label>
                <input
                  type="number"
                  id="postUserId"
                  name="userId"
                  min="1"
                  required
                />
              </div>
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-plus"></i> Create Post
              </button>
            </form>
          </div>

          <!-- Posts List -->
          <div class="posts-container">
            <div class="section-header">
              <h3>Recent Posts</h3>
              <button id="refreshPosts" class="btn btn-secondary">
                <i class="fas fa-refresh"></i> Refresh
              </button>
            </div>
            <div id="postsList" class="posts-grid"></div>
          </div>
        </section>

        <!-- API Response Section -->
        <section class="response-section">
          <h2><i class="fas fa-terminal"></i> API Response</h2>
          <div class="response-container">
            <pre id="apiResponse" class="response-output">
API responses will appear here...</pre
            >
          </div>
        </section>
      </div>
    </main>

    <footer class="footer">
      <div class="container">
        <p>
          &copy; 2024 API Interface Dashboard. Built with vanilla JavaScript and
          REST APIs.
        </p>
      </div>
    </footer>

    <script src="css/js/script.js"></script>
  </body>
</html>
