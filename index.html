<!DOCTYPE html>
<html lang="fr">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Gestion des Professeurs - TP</title>
    <link rel="stylesheet" href="css/style.css" />
  </head>
  <body>
    <header>
      <h1>Gestion des Professeurs et Salles TP</h1>
    </header>

    <main>
      <section class="add-prof-section">
        <h2>Ajouter un Professeur</h2>
        <form id="profForm">
          <div class="form-group">
            <label for="profName">Nom du Professeur:</label>
            <input type="text" id="profName" required />
          </div>
          <div class="form-group">
            <label for="profMatiere">Matière:</label>
            <input type="text" id="profMatiere" required />
          </div>
          <div class="form-group">
            <label for="profSalle">Salle TP:</label>
            <input type="text" id="profSalle" required />
          </div>
          <button type="submit">Ajouter Professeur</button>
        </form>
      </section>

      <section class="prof-list-section">
        <h2>Liste des Professeurs</h2>
        <div id="profList" class="prof-container"></div>
      </section>
    </main>

    <script src="css/js/script.js"></script>
  </body>
</html>
