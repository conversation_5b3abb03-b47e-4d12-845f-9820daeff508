// Simple JavaScript for Professor Management with JSONPlaceholder API
// API Configuration
const API_BASE_URL = "https://jsonplaceholder.typicode.com";

// Array to store professors
let professeurs = [];

// DOM Elements
const profForm = document.getElementById("profForm");
const profList = document.getElementById("profList");
const profNameInput = document.getElementById("profName");
const profMatiereInput = document.getElementById("profMatiere");
const profSalleInput = document.getElementById("profSalle");

// Subjects and rooms arrays for mapping
const matieres = [
  "Programmation Web",
  "Base de Données",
  "Réseaux Informatiques",
  "Algorithmique",
  "Systèmes d'Exploitation",
  "Intelligence Artificielle",
  "Développement Mobile",
  "Sécurité Informatique",
  "Architecture Logicielle",
  "Data Science",
];

const salles = [
  "TP-101",
  "TP-102",
  "TP-201",
  "TP-202",
  "TP-301",
  "TP-302",
  "TP-401",
  "TP-402",
  "LAB-A",
  "LAB-B",
];

// Function to get random element from array
function getRandomElement(array) {
  return array[Math.floor(Math.random() * array.length)];
}

// Function to fetch professors from JSONPlaceholder API
async function fetchProfesseurs() {
  try {
    showMessage("Chargement des professeurs...", "info");

    const response = await fetch(`${API_BASE_URL}/users`);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const users = await response.json();

    // Transform users data to professors format
    professeurs = users.map((user) => ({
      id: user.id,
      nom: user.name,
      matiere: getRandomElement(matieres),
      salle: getRandomElement(salles),
      email: user.email,
      phone: user.phone,
    }));

    afficherProfesseurs();
    showMessage(
      `${professeurs.length} professeurs chargés avec succès!`,
      "success"
    );
  } catch (error) {
    console.error("Erreur lors du chargement des professeurs:", error);
    showMessage("Erreur lors du chargement des données", "error");

    // Fallback to sample data if API fails
    professeurs = [
      {
        id: 1,
        nom: "Dr. Martin Dupont",
        matiere: "Programmation Web",
        salle: "TP-101",
      },
      {
        id: 2,
        nom: "Prof. Sarah Leblanc",
        matiere: "Base de Données",
        salle: "TP-205",
      },
      {
        id: 3,
        nom: "Dr. Ahmed Benali",
        matiere: "Réseaux Informatiques",
        salle: "TP-302",
      },
    ];
    afficherProfesseurs();
  }
}

// Function to generate unique ID
function generateId() {
  return professeurs.length > 0
    ? Math.max(...professeurs.map((p) => p.id)) + 1
    : 1;
}

// Function to add a new professor using API
async function ajouterProfesseur(nom, matiere, salle) {
  try {
    showMessage("Ajout du professeur en cours...", "info");

    // Create new professor data
    const nouveauProf = {
      name: nom,
      email: `${nom.toLowerCase().replace(/\s+/g, ".")}@university.edu`,
      phone: `+33 ${Math.floor(Math.random() * 900000000) + 100000000}`,
      website: `prof-${nom.toLowerCase().replace(/\s+/g, "-")}.university.edu`,
    };

    // Send POST request to API
    const response = await fetch(`${API_BASE_URL}/users`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(nouveauProf),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const createdUser = await response.json();

    // Add to local array with professor-specific data
    const profToAdd = {
      id: createdUser.id,
      nom: nom,
      matiere: matiere,
      salle: salle,
      email: nouveauProf.email,
      phone: nouveauProf.phone,
    };

    professeurs.push(profToAdd);
    afficherProfesseurs();

    showMessage(`Professeur ${nom} ajouté avec succès!`, "success");
  } catch (error) {
    console.error("Erreur lors de l'ajout du professeur:", error);
    showMessage("Erreur lors de l'ajout du professeur", "error");
  }
}

// Function to delete a professor using API
async function supprimerProfesseur(id) {
  try {
    const profIndex = professeurs.findIndex((p) => p.id === id);
    if (profIndex === -1) {
      showMessage("Professeur non trouvé!", "error");
      return;
    }

    const profNom = professeurs[profIndex].nom;
    showMessage(`Suppression de ${profNom} en cours...`, "info");

    // Send DELETE request to API
    const response = await fetch(`${API_BASE_URL}/users/${id}`, {
      method: "DELETE",
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // Remove from local array
    professeurs.splice(profIndex, 1);
    afficherProfesseurs();
    showMessage(`Professeur ${profNom} supprimé avec succès!`, "success");
  } catch (error) {
    console.error("Erreur lors de la suppression du professeur:", error);
    showMessage("Erreur lors de la suppression du professeur", "error");
  }
}

// Function to display all professors
function afficherProfesseurs() {
  if (professeurs.length === 0) {
    profList.innerHTML =
      '<div class="empty-message">Aucun professeur enregistré. <br><small>Chargement depuis l\'API en cours...</small></div>';
    return;
  }

  profList.innerHTML = professeurs
    .map(
      (prof) => `
        <div class="prof-card">
            <h3>${prof.nom}</h3>
            <p>Matière: <span class="matiere">${prof.matiere}</span></p>
            <p>Salle TP: <span class="salle">${prof.salle}</span></p>
            ${prof.email ? `<p><small>📧 ${prof.email}</small></p>` : ""}
            ${prof.phone ? `<p><small>📞 ${prof.phone}</small></p>` : ""}
            <button class="delete-btn" onclick="supprimerProfesseur(${
              prof.id
            })">
                Supprimer
            </button>
        </div>
    `
    )
    .join("");
}

// Function to show messages
function showMessage(message, type = "info") {
  // Create message element
  const messageDiv = document.createElement("div");
  messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 1rem 1.5rem;
        background: ${
          type === "success"
            ? "#27ae60"
            : type === "error"
            ? "#e74c3c"
            : "#3498db"
        };
        color: white;
        border-radius: 5px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        z-index: 1000;
        animation: slideIn 0.3s ease;
    `;
  messageDiv.textContent = message;

  // Add animation styles
  const style = document.createElement("style");
  style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
    `;
  if (!document.querySelector("#message-styles")) {
    style.id = "message-styles";
    document.head.appendChild(style);
  }

  document.body.appendChild(messageDiv);

  // Remove message after 3 seconds
  setTimeout(() => {
    messageDiv.style.animation = "slideOut 0.3s ease";
    setTimeout(() => messageDiv.remove(), 300);
  }, 3000);
}

// Form submission event
profForm.addEventListener("submit", function (e) {
  e.preventDefault();

  const nom = profNameInput.value.trim();
  const matiere = profMatiereInput.value.trim();
  const salle = profSalleInput.value.trim();

  // Validation
  if (!nom || !matiere || !salle) {
    showMessage("Veuillez remplir tous les champs!", "error");
    return;
  }

  // Check if professor already exists
  const profExiste = professeurs.some(
    (p) =>
      p.nom.toLowerCase() === nom.toLowerCase() &&
      p.matiere.toLowerCase() === matiere.toLowerCase()
  );

  if (profExiste) {
    showMessage("Ce professeur existe déjà pour cette matière!", "error");
    return;
  }

  // Add professor
  ajouterProfesseur(nom, matiere, salle);

  // Reset form
  profForm.reset();
});

// Initialize the application
document.addEventListener("DOMContentLoaded", function () {
  console.log("🎓 Application de Gestion des Professeurs chargée!");
  afficherProfesseurs();
});

// Additional utility functions
function rechercherProfesseur(terme) {
  return professeurs.filter(
    (prof) =>
      prof.nom.toLowerCase().includes(terme.toLowerCase()) ||
      prof.matiere.toLowerCase().includes(terme.toLowerCase()) ||
      prof.salle.toLowerCase().includes(terme.toLowerCase())
  );
}

function obtenirStatistiques() {
  const stats = {
    totalProfesseurs: professeurs.length,
    matieres: [...new Set(professeurs.map((p) => p.matiere))],
    salles: [...new Set(professeurs.map((p) => p.salle))],
  };

  console.log("📊 Statistiques:", stats);
  return stats;
}
