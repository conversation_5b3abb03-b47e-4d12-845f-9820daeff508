// Simple JavaScript for Professor Management
// Array to store professors
let professeurs = [];

// DOM Elements
const profForm = document.getElementById("profForm");
const profList = document.getElementById("profList");
const profNameInput = document.getElementById("profName");
const profMatiereInput = document.getElementById("profMatiere");
const profSalleInput = document.getElementById("profSalle");

// Add some sample data
professeurs = [
  {
    id: 1,
    nom: "Dr. <PERSON>",
    matiere: "Programmation Web",
    salle: "TP-101",
  },
  {
    id: 2,
    nom: "Prof<PERSON> <PERSON>",
    matiere: "Base de Données",
    salle: "TP-205",
  },
  {
    id: 3,
    nom: "Dr. <PERSON>",
    matiere: "Réseaux Informatiques",
    salle: "TP-302",
  },
];

// Function to generate unique ID
function generateId() {
  return professeurs.length > 0
    ? Math.max(...professeurs.map((p) => p.id)) + 1
    : 1;
}

// Function to add a new professor
function ajouterProfesseur(nom, matiere, salle) {
  const nouveauProf = {
    id: generateId(),
    nom: nom,
    matiere: matiere,
    salle: salle,
  };

  professeurs.push(nouveauProf);
  afficherProfesseurs();

  // Show success message
  showMessage(`Professeur ${nom} ajouté avec succès!`, "success");
}

// Function to delete a professor
function supprimerProfesseur(id) {
  const profIndex = professeurs.findIndex((p) => p.id === id);
  if (profIndex !== -1) {
    const profNom = professeurs[profIndex].nom;
    professeurs.splice(profIndex, 1);
    afficherProfesseurs();
    showMessage(`Professeur ${profNom} supprimé!`, "info");
  }
}

// Function to display all professors
function afficherProfesseurs() {
  if (professeurs.length === 0) {
    profList.innerHTML =
      '<div class="empty-message">Aucun professeur enregistré</div>';
    return;
  }

  profList.innerHTML = professeurs
    .map(
      (prof) => `
        <div class="prof-card">
            <h3>${prof.nom}</h3>
            <p>Matière: <span class="matiere">${prof.matiere}</span></p>
            <p>Salle TP: <span class="salle">${prof.salle}</span></p>
            <button class="delete-btn" onclick="supprimerProfesseur(${prof.id})">
                Supprimer
            </button>
        </div>
    `
    )
    .join("");
}

// Function to show messages
function showMessage(message, type = "info") {
  // Create message element
  const messageDiv = document.createElement("div");
  messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 1rem 1.5rem;
        background: ${
          type === "success"
            ? "#27ae60"
            : type === "error"
            ? "#e74c3c"
            : "#3498db"
        };
        color: white;
        border-radius: 5px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        z-index: 1000;
        animation: slideIn 0.3s ease;
    `;
  messageDiv.textContent = message;

  // Add animation styles
  const style = document.createElement("style");
  style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
    `;
  if (!document.querySelector("#message-styles")) {
    style.id = "message-styles";
    document.head.appendChild(style);
  }

  document.body.appendChild(messageDiv);

  // Remove message after 3 seconds
  setTimeout(() => {
    messageDiv.style.animation = "slideOut 0.3s ease";
    setTimeout(() => messageDiv.remove(), 300);
  }, 3000);
}

// Form submission event
profForm.addEventListener("submit", function (e) {
  e.preventDefault();

  const nom = profNameInput.value.trim();
  const matiere = profMatiereInput.value.trim();
  const salle = profSalleInput.value.trim();

  // Validation
  if (!nom || !matiere || !salle) {
    showMessage("Veuillez remplir tous les champs!", "error");
    return;
  }

  // Check if professor already exists
  const profExiste = professeurs.some(
    (p) =>
      p.nom.toLowerCase() === nom.toLowerCase() &&
      p.matiere.toLowerCase() === matiere.toLowerCase()
  );

  if (profExiste) {
    showMessage("Ce professeur existe déjà pour cette matière!", "error");
    return;
  }

  // Add professor
  ajouterProfesseur(nom, matiere, salle);

  // Reset form
  profForm.reset();
});

// Initialize the application
document.addEventListener("DOMContentLoaded", function () {
  console.log("🎓 Application de Gestion des Professeurs chargée!");
  afficherProfesseurs();
});

// Additional utility functions
function rechercherProfesseur(terme) {
  return professeurs.filter(
    (prof) =>
      prof.nom.toLowerCase().includes(terme.toLowerCase()) ||
      prof.matiere.toLowerCase().includes(terme.toLowerCase()) ||
      prof.salle.toLowerCase().includes(terme.toLowerCase())
  );
}

function obtenirStatistiques() {
  const stats = {
    totalProfesseurs: professeurs.length,
    matieres: [...new Set(professeurs.map((p) => p.matiere))],
    salles: [...new Set(professeurs.map((p) => p.salle))],
  };

  console.log("📊 Statistiques:", stats);
  return stats;
}
