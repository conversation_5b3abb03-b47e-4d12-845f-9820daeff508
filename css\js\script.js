// Simple Professor Management with API
const API_URL = "https://jsonplaceholder.typicode.com/users";
let professeurs = [];

// Get form elements
const form = document.getElementById("profForm");
const list = document.getElementById("profList");

// Subject options
const subjects = [
  "Programmation Web",
  "Base de Données",
  "Réseaux",
  "Algorithmique",
];
const rooms = ["TP-101", "TP-102", "TP-201", "TP-202"];

// Get random item
function random(arr) {
  return arr[Math.floor(Math.random() * arr.length)];
}

// Load professors from API
async function loadProfs() {
  try {
    const response = await fetch(API_URL);
    const users = await response.json();

    professeurs = users.map((user) => ({
      id: user.id,
      nom: user.name,
      matiere: random(subjects),
      salle: random(rooms),
    }));

    showProfs();
    showMsg(`${professeurs.length} professeurs chargés!`, "success");
  } catch (error) {
    showMsg("Erreur de chargement", "error");
  }
}

// Add new professor
async function addProf(nom, matiere, salle) {
  try {
    const response = await fetch(API_URL, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ name: nom }),
    });

    const newUser = await response.json();
    const newProf = { id: newUser.id, nom, matiere, salle };

    professeurs.push(newProf);
    showProfs();
    showMsg(`${nom} ajouté!`, "success");
  } catch (error) {
    showMsg("Erreur ajout", "error");
  }
}

// Delete professor
async function deleteProf(id) {
  try {
    await fetch(`${API_URL}/${id}`, { method: "DELETE" });

    professeurs = professeurs.filter((p) => p.id !== id);
    showProfs();
    showMsg("Professeur supprimé!", "success");
  } catch (error) {
    showMsg("Erreur suppression", "error");
  }
}

// Function to display all professors
function afficherProfesseurs() {
  if (professeurs.length === 0) {
    profList.innerHTML =
      '<div class="empty-message">Aucun professeur enregistré. <br><small>Chargement depuis l\'API en cours...</small></div>';
    return;
  }

  profList.innerHTML = professeurs
    .map(
      (prof) => `
        <div class="prof-card">
            <h3>${prof.nom}</h3>
            <p>Matière: <span class="matiere">${prof.matiere}</span></p>
            <p>Salle TP: <span class="salle">${prof.salle}</span></p>
            ${prof.email ? `<p><small>📧 ${prof.email}</small></p>` : ""}
            ${prof.phone ? `<p><small>📞 ${prof.phone}</small></p>` : ""}
            <button class="delete-btn" onclick="supprimerProfesseur(${
              prof.id
            })">
                Supprimer
            </button>
        </div>
    `
    )
    .join("");
}

// Function to show messages
function showMessage(message, type = "info") {
  // Create message element
  const messageDiv = document.createElement("div");
  messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 1rem 1.5rem;
        background: ${
          type === "success"
            ? "#27ae60"
            : type === "error"
            ? "#e74c3c"
            : "#3498db"
        };
        color: white;
        border-radius: 5px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        z-index: 1000;
        animation: slideIn 0.3s ease;
    `;
  messageDiv.textContent = message;

  // Add animation styles
  const style = document.createElement("style");
  style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
    `;
  if (!document.querySelector("#message-styles")) {
    style.id = "message-styles";
    document.head.appendChild(style);
  }

  document.body.appendChild(messageDiv);

  // Remove message after 3 seconds
  setTimeout(() => {
    messageDiv.style.animation = "slideOut 0.3s ease";
    setTimeout(() => messageDiv.remove(), 300);
  }, 3000);
}

// Form submission event
profForm.addEventListener("submit", function (e) {
  e.preventDefault();

  const nom = profNameInput.value.trim();
  const matiere = profMatiereInput.value.trim();
  const salle = profSalleInput.value.trim();

  // Validation
  if (!nom || !matiere || !salle) {
    showMessage("Veuillez remplir tous les champs!", "error");
    return;
  }

  // Check if professor already exists
  const profExiste = professeurs.some(
    (p) =>
      p.nom.toLowerCase() === nom.toLowerCase() &&
      p.matiere.toLowerCase() === matiere.toLowerCase()
  );

  if (profExiste) {
    showMessage("Ce professeur existe déjà pour cette matière!", "error");
    return;
  }

  // Add professor
  ajouterProfesseur(nom, matiere, salle);

  // Reset form
  profForm.reset();
});

// Initialize the application
document.addEventListener("DOMContentLoaded", function () {
  console.log("🎓 Application de Gestion des Professeurs chargée!");
  afficherProfesseurs();
});

// Additional utility functions
function rechercherProfesseur(terme) {
  return professeurs.filter(
    (prof) =>
      prof.nom.toLowerCase().includes(terme.toLowerCase()) ||
      prof.matiere.toLowerCase().includes(terme.toLowerCase()) ||
      prof.salle.toLowerCase().includes(terme.toLowerCase())
  );
}

function obtenirStatistiques() {
  const stats = {
    totalProfesseurs: professeurs.length,
    matieres: [...new Set(professeurs.map((p) => p.matiere))],
    salles: [...new Set(professeurs.map((p) => p.salle))],
  };

  console.log("📊 Statistiques:", stats);
  return stats;
}
