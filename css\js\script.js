// API Configuration
const API_BASE_URL = "https://jsonplaceholder.typicode.com";

// DOM Elements
const apiStatus = document.getElementById("apiStatus");
const loadingSpinner = document.getElementById("loadingSpinner");
const usersList = document.getElementById("usersList");
const postsList = document.getElementById("postsList");
const apiResponse = document.getElementById("apiResponse");
const addUserForm = document.getElementById("addUserForm");
const addPostForm = document.getElementById("addPostForm");
const refreshUsersBtn = document.getElementById("refreshUsers");
const refreshPostsBtn = document.getElementById("refreshPosts");

// State Management
let users = [];
let posts = [];

// Utility Functions
function showLoading() {
  loadingSpinner.classList.remove("hidden");
}

function hideLoading() {
  loadingSpinner.classList.add("hidden");
}

function updateApiResponse(data, operation = "API Response") {
  const timestamp = new Date().toLocaleString();
  const responseText = `[${timestamp}] ${operation}:\n${JSON.stringify(
    data,
    null,
    2
  )}`;
  apiResponse.textContent = responseText;
}

function showNotification(message, type = "info") {
  // Create notification element
  const notification = document.createElement("div");
  notification.className = `notification notification-${type}`;
  notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 1rem 1.5rem;
        background: ${
          type === "success"
            ? "#10b981"
            : type === "error"
            ? "#ef4444"
            : "#3b82f6"
        };
        color: white;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 1000;
        animation: slideIn 0.3s ease;
    `;
  notification.textContent = message;

  document.body.appendChild(notification);

  // Remove notification after 3 seconds
  setTimeout(() => {
    notification.style.animation = "slideOut 0.3s ease";
    setTimeout(() => notification.remove(), 300);
  }, 3000);
}

// API Functions
async function checkApiStatus() {
  try {
    const response = await fetch(`${API_BASE_URL}/users/1`);
    const statusDot = apiStatus.querySelector(".status-dot");
    const statusText = apiStatus.querySelector(".status-text");

    if (response.ok) {
      statusDot.className = "status-dot online";
      statusText.textContent = "API Online";
      return true;
    } else {
      throw new Error("API not responding");
    }
  } catch (error) {
    const statusDot = apiStatus.querySelector(".status-dot");
    const statusText = apiStatus.querySelector(".status-text");
    statusDot.className = "status-dot offline";
    statusText.textContent = "API Offline";
    return false;
  }
}

async function fetchUsers() {
  try {
    showLoading();
    const response = await fetch(`${API_BASE_URL}/users`);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    users = await response.json();
    updateApiResponse(users, "Fetched Users");
    renderUsers();
    showNotification("Users loaded successfully!", "success");
  } catch (error) {
    console.error("Error fetching users:", error);
    updateApiResponse({ error: error.message }, "Error Fetching Users");
    showNotification("Failed to load users", "error");
  } finally {
    hideLoading();
  }
}

async function fetchPosts() {
  try {
    const response = await fetch(`${API_BASE_URL}/posts?_limit=10`);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    posts = await response.json();
    updateApiResponse(posts, "Fetched Posts");
    renderPosts();
    showNotification("Posts loaded successfully!", "success");
  } catch (error) {
    console.error("Error fetching posts:", error);
    updateApiResponse({ error: error.message }, "Error Fetching Posts");
    showNotification("Failed to load posts", "error");
  }
}

async function createUser(userData) {
  try {
    const response = await fetch(`${API_BASE_URL}/users`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(userData),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const newUser = await response.json();
    updateApiResponse(newUser, "Created User");

    // Add to local users array (simulate real addition)
    newUser.id = users.length + 1;
    users.unshift(newUser);
    renderUsers();

    showNotification("User created successfully!", "success");
    return newUser;
  } catch (error) {
    console.error("Error creating user:", error);
    updateApiResponse({ error: error.message }, "Error Creating User");
    showNotification("Failed to create user", "error");
    throw error;
  }
}

async function createPost(postData) {
  try {
    const response = await fetch(`${API_BASE_URL}/posts`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(postData),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const newPost = await response.json();
    updateApiResponse(newPost, "Created Post");

    // Add to local posts array (simulate real addition)
    newPost.id = posts.length + 1;
    posts.unshift(newPost);
    renderPosts();

    showNotification("Post created successfully!", "success");
    return newPost;
  } catch (error) {
    console.error("Error creating post:", error);
    updateApiResponse({ error: error.message }, "Error Creating Post");
    showNotification("Failed to create post", "error");
    throw error;
  }
}

async function deleteUser(userId) {
  try {
    const response = await fetch(`${API_BASE_URL}/users/${userId}`, {
      method: "DELETE",
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    updateApiResponse({ message: `User ${userId} deleted` }, "Deleted User");

    // Remove from local users array
    users = users.filter((user) => user.id !== userId);
    renderUsers();

    showNotification("User deleted successfully!", "success");
  } catch (error) {
    console.error("Error deleting user:", error);
    updateApiResponse({ error: error.message }, "Error Deleting User");
    showNotification("Failed to delete user", "error");
  }
}

async function deletePost(postId) {
  try {
    const response = await fetch(`${API_BASE_URL}/posts/${postId}`, {
      method: "DELETE",
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    updateApiResponse({ message: `Post ${postId} deleted` }, "Deleted Post");

    // Remove from local posts array
    posts = posts.filter((post) => post.id !== postId);
    renderPosts();

    showNotification("Post deleted successfully!", "success");
  } catch (error) {
    console.error("Error deleting post:", error);
    updateApiResponse({ error: error.message }, "Error Deleting Post");
    showNotification("Failed to delete post", "error");
  }
}

// Render Functions
function renderUsers() {
  if (users.length === 0) {
    usersList.innerHTML =
      '<p class="no-data">No users found. Try refreshing or check your connection.</p>';
    return;
  }

  usersList.innerHTML = users
    .map(
      (user) => `
        <div class="user-card">
            <h4>${user.name}</h4>
            <p><i class="fas fa-envelope"></i> ${user.email}</p>
            <p><i class="fas fa-phone"></i> ${user.phone || "N/A"}</p>
            <p><i class="fas fa-globe"></i> ${user.website || "N/A"}</p>
            <div class="user-actions">
                <button class="btn btn-edit" onclick="editUser(${user.id})">
                    <i class="fas fa-edit"></i> Edit
                </button>
                <button class="btn btn-danger" onclick="deleteUser(${user.id})">
                    <i class="fas fa-trash"></i> Delete
                </button>
            </div>
        </div>
    `
    )
    .join("");
}

function renderPosts() {
  if (posts.length === 0) {
    postsList.innerHTML =
      '<p class="no-data">No posts found. Try refreshing or check your connection.</p>';
    return;
  }

  postsList.innerHTML = posts
    .map(
      (post) => `
        <div class="post-card">
            <h4>${post.title}</h4>
            <p>${post.body}</p>
            <div class="post-meta">
                <span><i class="fas fa-user"></i> User ID: ${post.userId}</span>
                <span><i class="fas fa-hashtag"></i> Post ID: ${post.id}</span>
            </div>
            <div class="post-actions">
                <button class="btn btn-edit" onclick="editPost(${post.id})">
                    <i class="fas fa-edit"></i> Edit
                </button>
                <button class="btn btn-danger" onclick="deletePost(${post.id})">
                    <i class="fas fa-trash"></i> Delete
                </button>
            </div>
        </div>
    `
    )
    .join("");
}

// Edit Functions (Placeholder implementations)
function editUser(userId) {
  const user = users.find((u) => u.id === userId);
  if (user) {
    // Fill form with user data
    document.getElementById("userName").value = user.name;
    document.getElementById("userEmail").value = user.email;
    document.getElementById("userPhone").value = user.phone || "";

    showNotification(`Editing user: ${user.name}`, "info");
    updateApiResponse(user, "User Selected for Editing");
  }
}

function editPost(postId) {
  const post = posts.find((p) => p.id === postId);
  if (post) {
    // Fill form with post data
    document.getElementById("postTitle").value = post.title;
    document.getElementById("postBody").value = post.body;
    document.getElementById("postUserId").value = post.userId;

    showNotification(`Editing post: ${post.title}`, "info");
    updateApiResponse(post, "Post Selected for Editing");
  }
}

// Event Listeners
document.addEventListener("DOMContentLoaded", async function () {
  // Add CSS animations
  const style = document.createElement("style");
  style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
        .no-data {
            text-align: center;
            color: #6b7280;
            font-style: italic;
            padding: 2rem;
        }
    `;
  document.head.appendChild(style);

  // Initialize the application
  await checkApiStatus();
  await fetchUsers();
  await fetchPosts();
});

// Form Submissions
addUserForm.addEventListener("submit", async function (e) {
  e.preventDefault();

  const formData = new FormData(addUserForm);
  const userData = {
    name: formData.get("name"),
    email: formData.get("email"),
    phone: formData.get("phone"),
  };

  try {
    await createUser(userData);
    addUserForm.reset();
  } catch (error) {
    // Error already handled in createUser function
  }
});

addPostForm.addEventListener("submit", async function (e) {
  e.preventDefault();

  const formData = new FormData(addPostForm);
  const postData = {
    title: formData.get("title"),
    body: formData.get("body"),
    userId: parseInt(formData.get("userId")),
  };

  try {
    await createPost(postData);
    addPostForm.reset();
  } catch (error) {
    // Error already handled in createPost function
  }
});

// Refresh Buttons
refreshUsersBtn.addEventListener("click", fetchUsers);
refreshPostsBtn.addEventListener("click", fetchPosts);

// Additional API demonstration functions
async function demonstrateApiCalls() {
  console.log("🚀 API Interface Dashboard Loaded!");
  console.log("Available API endpoints:");
  console.log("- GET /users - Fetch all users");
  console.log("- POST /users - Create new user");
  console.log("- DELETE /users/:id - Delete user");
  console.log("- GET /posts - Fetch all posts");
  console.log("- POST /posts - Create new post");
  console.log("- DELETE /posts/:id - Delete post");

  // Test API connectivity
  const isOnline = await checkApiStatus();
  if (isOnline) {
    console.log("✅ API is online and ready!");
  } else {
    console.log("❌ API is offline or unreachable");
  }
}

// Call demonstration function
demonstrateApiCalls();

// Global error handler
window.addEventListener("error", function (e) {
  console.error("Global error:", e.error);
  updateApiResponse(
    {
      error: "Application Error",
      message: e.error.message,
      stack: e.error.stack,
    },
    "Application Error"
  );
});

// Handle unhandled promise rejections
window.addEventListener("unhandledrejection", function (e) {
  console.error("Unhandled promise rejection:", e.reason);
  updateApiResponse(
    {
      error: "Unhandled Promise Rejection",
      reason: e.reason,
    },
    "Promise Error"
  );
});
