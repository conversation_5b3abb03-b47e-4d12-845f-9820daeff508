/* Simple CSS for Professor Management */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: Arial, sans-serif;
  background-color: #f4f4f4;
  color: #333;
  line-height: 1.6;
}

header {
  background-color: #2c3e50;
  color: white;
  text-align: center;
  padding: 1rem;
}

header h1 {
  font-size: 2rem;
}

main {
  max-width: 800px;
  margin: 2rem auto;
  padding: 0 1rem;
}

section {
  background: white;
  margin-bottom: 2rem;
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

h2 {
  color: #2c3e50;
  margin-bottom: 1rem;
  border-bottom: 2px solid #3498db;
  padding-bottom: 0.5rem;
}

.form-group {
  margin-bottom: 1rem;
}

label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: bold;
  color: #555;
}

input[type="text"] {
  width: 100%;
  padding: 0.8rem;
  border: 2px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

input[type="text"]:focus {
  outline: none;
  border-color: #3498db;
}

button {
  background-color: #3498db;
  color: white;
  padding: 0.8rem 1.5rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s;
}

button:hover {
  background-color: #2980b9;
}

.prof-container {
  display: grid;
  gap: 1rem;
}

.prof-card {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1.5rem;
  transition: transform 0.2s, box-shadow 0.2s;
}

.prof-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.prof-card h3 {
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.prof-card p {
  margin-bottom: 0.3rem;
  color: #666;
}

.prof-card .matiere {
  font-weight: bold;
  color: #e74c3c;
}

.prof-card .salle {
  font-weight: bold;
  color: #27ae60;
}

.delete-btn {
  background-color: #e74c3c;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 1rem;
  font-size: 0.9rem;
}

.delete-btn:hover {
  background-color: #c0392b;
}

.empty-message {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 2rem;
}

@media (max-width: 600px) {
  main {
    margin: 1rem auto;
    padding: 0 0.5rem;
  }

  section {
    padding: 1rem;
  }

  header h1 {
    font-size: 1.5rem;
  }
}
